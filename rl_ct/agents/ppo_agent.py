"""
PPO agent implementation using Ray RLlib.
"""

import os
from typing import Dict, <PERSON>, Op<PERSON>, Tu<PERSON>, Union
import numpy as np
import ray
from ray import tune
from ray.rllib.algorithms.ppo import PPO, PPOConfig
from ray.rllib.models import ModelCatalog
from ray.rllib.policy.policy import Policy
from ray.tune.registry import register_env

from rl_ct.agents.base_agent import BaseAgent
from rl_ct.envs import CryptoTradingEnv
from rl_ct.models.rllib_models import RLlibTransformerModel, RLlibMLPModel
from rl_ct.utils.logger import get_logger
from rl_ct.utils.config import load_config

logger = get_logger(__name__)


class PPOAgent(BaseAgent):
    """PPO agent using Ray RLlib."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize PPO agent.
        
        Args:
            config: Agent configuration
        """
        super().__init__(config)
        
        # Initialize Ray if not already initialized
        if not ray.is_initialized():
            ray.init(
                local_mode=self.config.get('local_mode', False),
                ignore_reinit_error=True
            )
            
        # Register environment and models
        self._register_components()
        
        # Create PPO configuration
        self.ppo_config = self._create_ppo_config()
        
        # Initialize algorithm
        self.algorithm: Optional[PPO] = None
        self.policy: Optional[Policy] = None
        
        logger.info("PPOAgent initialized")
        
    def _register_components(self) -> None:
        """Register environment and models with Ray."""
        # Register environment
        def env_creator(env_config):
            return CryptoTradingEnv(env_config)
            
        register_env("CryptoTradingEnv", env_creator)
        
        # Register models
        ModelCatalog.register_custom_model("transformer", RLlibTransformerModel)
        ModelCatalog.register_custom_model("mlp", RLlibMLPModel)
        
        logger.debug("Components registered with Ray")
        
    def _create_ppo_config(self) -> PPOConfig:
        """Create PPO configuration."""
        config = PPOConfig()
        
        # Environment configuration
        env_config = self.config.get('env', {})
        config = config.environment(
            env="CryptoTradingEnv",
            env_config=env_config
        )
        
        # Algorithm configuration
        algo_config = self.config.get('algorithm', {})
        config = config.training(
            lr=algo_config.get('learning_rate', 3e-4),
            train_batch_size=algo_config.get('batch_size', 4000),
            # sgd_minibatch_size=algo_config.get('sgd_minibatch_size', 128),
            num_epochs=algo_config.get('n_epochs', 10),
            gamma=algo_config.get('gamma', 0.99),
            lambda_=algo_config.get('gae_lambda', 0.95),
            clip_param=algo_config.get('clip_range', 0.2),
            vf_clip_param=algo_config.get('clip_range_vf', 10.0),
            entropy_coeff=algo_config.get('ent_coef', 0.01),
            vf_loss_coeff=algo_config.get('vf_coef', 0.5),
            grad_clip=algo_config.get('max_grad_norm', 0.5),
            kl_coeff=algo_config.get('kl_coef', 0.2),
            kl_target=algo_config.get('target_kl', 0.01),
        )
        
        # Model configuration
        model_config = self.config.get('model', {})
        model_type = model_config.get('type', 'mlp')
        
        config = config.training(
            model={
                "custom_model": model_type,
                "custom_model_config": model_config,
                "vf_share_layers": model_config.get('vf_share_layers', True),
            }
        )
        
        # Environment runners configuration (replaces deprecated rollouts)
        rollout_config = self.config.get('rollouts', {})
        ray_config = self.config.get('ray', {})

        # Support both old 'rollouts' and new 'ray' config sections for backward compatibility
        num_workers = rollout_config.get('num_workers') or ray_config.get('num_workers', 2)
        num_envs_per_worker = rollout_config.get('num_envs_per_worker') or ray_config.get('num_envs_per_worker', 1)

        # Support both 'resources' and 'ray' config sections for CPU allocation
        resource_config = self.config.get('resources', {})
        ray_resources = ray_config.get('resources', {})
        num_cpus_per_worker = resource_config.get('num_cpus_per_worker') or ray_config.get('num_cpus_per_worker', 1)

        config = config.env_runners(
            num_env_runners=num_workers,
            num_envs_per_env_runner=num_envs_per_worker,
            num_cpus_per_env_runner=num_cpus_per_worker,
            rollout_fragment_length=rollout_config.get('rollout_fragment_length', 200),
            batch_mode=rollout_config.get('batch_mode', 'complete_episodes'),
        )
        
        # Resource configuration (GPU only, CPU allocation moved to env_runners)
        ray_resources = ray_config.get('resources', {})

        # Support both 'resources' and 'ray' config sections for GPU allocation
        num_gpus = resource_config.get('num_gpus') or ray_config.get('num_gpus') or ray_resources.get('gpu', 0)

        config = config.resources(
            num_gpus=num_gpus,
        )
        
        # Evaluation configuration
        eval_config = self.config.get('evaluation', {})
        if eval_config.get('enabled', True):
            parallel_eval = eval_config.get('parallel', True)
            config = config.evaluation(
                evaluation_interval=eval_config.get('eval_freq', 10),
                evaluation_duration=eval_config.get('eval_episodes', 10),
                evaluation_duration_unit='episodes',
                evaluation_parallel_to_training=parallel_eval,
                evaluation_num_env_runners=1 if parallel_eval else 0,
                evaluation_config={
                    "explore": False,
                    "env_config": {
                        **env_config,
                        "regime": "evaluation"
                    }
                }
            )
        
        # Debugging configuration
        debug_config = self.config.get('debugging', {})
        config = config.debugging(
            log_level=debug_config.get('log_level', 'WARN'),
            seed=debug_config.get('seed', 42),
        )
        
        return config
        
    def _build_algorithm(self) -> None:
        """Build the PPO algorithm."""
        if self.algorithm is None:
            self.algorithm = self.ppo_config.build_algo()
            self.policy = self.algorithm.get_policy()
            logger.info("PPO algorithm built")
            
    def predict(
        self,
        observation: np.ndarray,
        deterministic: bool = False
    ) -> Tuple[np.ndarray, Optional[Dict[str, Any]]]:
        """
        Predict action from observation.
        
        Args:
            observation: Environment observation
            deterministic: Whether to use deterministic policy
            
        Returns:
            Tuple of (action, extra_info)
        """
        if self.algorithm is None:
            self._build_algorithm()
            
        # Compute action
        action = self.algorithm.compute_single_action(
            observation,
            explore=not deterministic,
            policy_id="default_policy"
        )
        
        return np.array([action]), None
        
    def learn(
        self,
        total_timesteps: int,
        callback: Optional[Any] = None,
        log_interval: int = 1000,
        eval_env: Optional[Any] = None,
        eval_freq: int = -1,
        n_eval_episodes: int = 5,
        tb_log_name: str = "run",
        eval_log_path: Optional[str] = None,
        reset_num_timesteps: bool = True,
    ) -> 'PPOAgent':
        """
        Train the agent.
        
        Args:
            total_timesteps: Total number of timesteps to train
            callback: Optional callback function
            log_interval: Logging interval
            eval_env: Evaluation environment
            eval_freq: Evaluation frequency
            n_eval_episodes: Number of evaluation episodes
            tb_log_name: Tensorboard log name
            eval_log_path: Evaluation log path
            reset_num_timesteps: Whether to reset timestep counter
            
        Returns:
            Self for method chaining
        """
        if self.algorithm is None:
            self._build_algorithm()
            
        if reset_num_timesteps:
            self.total_steps = 0
            
        # Calculate number of iterations
        train_batch_size = self.ppo_config.train_batch_size
        iterations = total_timesteps // train_batch_size
        
        logger.info(f"Starting training for {iterations} iterations ({total_timesteps} timesteps)")
        
        for i in range(iterations):
            # Train one iteration
            result = self.algorithm.train()
            
            # Update step count
            self.total_steps += train_batch_size
            
            # Log progress
            if (i + 1) % (log_interval // train_batch_size) == 0:
                episode_reward_mean = result.get('episode_reward_mean', 0)
                episode_len_mean = result.get('episode_len_mean', 0)
                
                logger.info(
                    f"Iteration {i+1}/{iterations} - "
                    f"Steps: {self.total_steps} - "
                    f"Reward: {episode_reward_mean:.2f} - "
                    f"Length: {episode_len_mean:.1f}"
                )
                
            # Callback
            if callback is not None:
                callback(result)
                
        logger.info("Training completed")
        return self
        
    def save(self, path: str) -> None:
        """
        Save agent to file.
        
        Args:
            path: Path to save agent
        """
        if self.algorithm is None:
            raise ValueError("Algorithm not initialized")
            
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Save algorithm checkpoint
        checkpoint_path = self.algorithm.save(path)
        logger.info(f"Agent saved to {checkpoint_path}")
        
    def load(self, path: str) -> None:
        """
        Load agent from file.
        
        Args:
            path: Path to load agent from
        """
        if self.algorithm is None:
            self._build_algorithm()
            
        # Restore algorithm from checkpoint
        self.algorithm.restore(path)
        self.policy = self.algorithm.get_policy()
        
        logger.info(f"Agent loaded from {path}")
        
    def get_stats(self) -> Dict[str, Any]:
        """
        Get training statistics.
        
        Returns:
            Dictionary of statistics
        """
        stats = super().get_stats()
        
        if self.algorithm is not None:
            # Get algorithm stats
            info = self.algorithm.get_policy().get_state()
            stats.update({
                'algorithm_initialized': True,
                'policy_state_keys': list(info.keys()) if isinstance(info, dict) else None,
            })
        else:
            stats['algorithm_initialized'] = False
            
        return stats
        
    def close(self) -> None:
        """Close the agent and cleanup resources."""
        if self.algorithm is not None:
            self.algorithm.stop()
            self.algorithm = None
            self.policy = None
            
        # Shutdown Ray if we initialized it
        if ray.is_initialized():
            ray.shutdown()
            
        logger.info("PPOAgent closed")
