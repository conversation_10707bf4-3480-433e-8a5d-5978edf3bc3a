# Default training configuration for RL-CT framework

# Experiment settings
experiment_name: "crypto_trading_ppo"
seed: 42
device: "auto"  # auto, cpu, cuda

# Environment settings
env:
  name: "CryptoTradingEnv"
  config_path: "configs/environment/default.yaml"
  num_envs: 8  # Number of parallel environments

# Algorithm configuration
algorithm:
  name: "PPO"
  
  # Learning parameters
  learning_rate: 3e-4
  lr_schedule: "constant"  # constant, linear, exponential
  
  # PPO specific parameters
  n_steps: 2048  # Number of steps to run for each environment per update
  batch_size: 64
  n_epochs: 10
  gamma: 0.99  # Discount factor
  gae_lambda: 0.95  # GAE lambda
  clip_range: 0.2  # PPO clipping parameter
  clip_range_vf: null  # Value function clipping (null to disable)
  ent_coef: 0.01  # Entropy coefficient
  vf_coef: 0.5  # Value function coefficient
  max_grad_norm: 0.5  # Gradient clipping
  
  # Target KL divergence
  target_kl: null  # Target KL divergence (null to disable)

# Model configuration
model:
  type: "transformer"  # transformer, mlp, lstm
  
  # Architecture parameters
  hidden_size: 256
  num_layers: 3
  num_heads: 8
  dropout: 0.1
  activation: "gelu"
  
  # Input/output dimensions (will be set automatically)
  input_size: null
  output_size: null
  
  # Model specific parameters
  transformer:
    d_model: 256
    d_ff: 512
    max_seq_length: 168
    use_positional_encoding: true
    
  mlp:
    hidden_layers: [256, 256, 128]
    
  lstm:
    hidden_size: 256
    num_layers: 2
    bidirectional: false

# Training parameters
training:
  total_timesteps: 1000000
  eval_freq: 10000  # Evaluate every N timesteps
  eval_episodes: 10  # Number of episodes for evaluation
  save_freq: 50000  # Save model every N timesteps
  log_interval: 1000  # Log training stats every N timesteps
  
  # Early stopping
  early_stopping:
    enabled: true
    patience: 10  # Number of evaluations without improvement
    min_delta: 0.01  # Minimum improvement threshold
    
  # Checkpointing
  checkpoint:
    save_best: true
    save_last: true
    save_freq: 50000

# Ray/RLlib specific settings
ray:
  num_workers: 4  # Number of worker processes
  num_envs_per_worker: 2  # Environments per worker
  num_gpus: 0  # Number of GPUs to use (0 for CPU only)
  num_cpus_per_worker: 1
  
  # Resource allocation
  resources:
    cpu: 4
    gpu: 0
    
  # Ray configuration
  local_mode: false  # Set to true for debugging
  ignore_reinit_error: true

# Logging and monitoring
logging:
  level: "INFO"
  log_dir: "logs"
  tensorboard: true
  wandb:
    enabled: false
    project: "rl-ct"
    entity: null
    tags: ["ppo", "crypto", "trading"]
    
  # What to log
  log_training_metrics: true
  log_evaluation_metrics: true
  log_model_weights: false
  log_gradients: false

# Data configuration
data:
  dataset_name: "default"
  data_dir: "data/processed"
  validation_split: 0.2
  
  # Data preprocessing
  preprocessing:
    normalize: true
    remove_outliers: true
    fill_missing: "forward"

# Evaluation configuration
evaluation:
  metrics:
    - "total_return"
    - "sharpe_ratio"
    - "max_drawdown"
    - "win_rate"
    - "profit_factor"
    - "calmar_ratio"
    
  # Backtesting settings
  backtest:
    start_date: null
    end_date: null
    initial_balance: 10000
    
# Hyperparameter optimization (optional)
hyperopt:
  enabled: false
  method: "optuna"  # optuna, ray_tune
  n_trials: 100
  
  # Parameters to optimize
  search_space:
    learning_rate:
      type: "loguniform"
      low: 1e-5
      high: 1e-2
    clip_range:
      type: "uniform"
      low: 0.1
      high: 0.4
    ent_coef:
      type: "loguniform"
      low: 1e-3
      high: 1e-1
