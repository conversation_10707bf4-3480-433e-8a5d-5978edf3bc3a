"""
Backtesting script for trained RL-CT models.
"""

import os
import sys
import json
from pathlib import Path
from typing import Optional, Dict, Any, List
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from rl_ct.agents.ppo_agent import PPOAgent
from rl_ct.envs import CryptoTradingEnv
from rl_ct.utils.config import load_config
from rl_ct.utils.logger import setup_logging, get_logger
from rl_ct.utils.metrics import TradingMetrics

logger = get_logger(__name__)


class Backtester:
    """Backtesting engine for RL trading models."""
    
    def __init__(
        self,
        model_path: str,
        config_path: str,
        initial_balance: float = 10000.0
    ):
        """
        Initialize backtester.
        
        Args:
            model_path: Path to trained model
            config_path: Path to configuration file
            initial_balance: Initial balance for backtesting
        """
        self.model_path = model_path
        self.config = load_config(config_path)
        self.initial_balance = initial_balance
        
        # Load agent
        self.agent = PPOAgent(self.config)
        self.agent.load(model_path)
        
        # Create backtesting environment
        env_config = self.config.get('env', {}).copy()
        env_config.update({
            'regime': 'backtesting',
            'initial_balance': initial_balance,
            'record_stats': True
        })
        self.env = CryptoTradingEnv(env_config)
        
        # Metrics tracker
        self.metrics = TradingMetrics()
        
        # Results storage
        self.results = {
            'trades': [],
            'equity_curve': [],
            'actions': [],
            'rewards': [],
            'positions': [],
            'balances': [],
            'timestamps': [],
        }
        
        logger.info(f"Backtester initialized with model: {model_path}")
        
    def run_backtest(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        deterministic: bool = True
    ) -> Dict[str, Any]:
        """
        Run backtesting simulation.
        
        Args:
            start_date: Start date for backtesting (YYYY-MM-DD)
            end_date: End date for backtesting (YYYY-MM-DD)
            deterministic: Whether to use deterministic policy
            
        Returns:
            Backtesting results
        """
        logger.info("Starting backtesting simulation")
        
        # Reset environment
        obs, info = self.env.reset()
        
        # Initialize tracking variables
        step = 0
        done = False
        total_reward = 0.0
        
        # Track initial state
        self._record_step(step, info, 0, 0.0)
        
        while not done:
            # Predict action
            action, _ = self.agent.predict(obs, deterministic=deterministic)
            
            # Take step
            obs, reward, terminated, truncated, info = self.env.step(action[0])
            done = terminated or truncated
            
            step += 1
            total_reward += reward
            
            # Record step data
            self._record_step(step, info, action[0], reward)
            
            # Update metrics
            self.metrics.update_equity(info.get('balance', 0) + info.get('unrealized_pnl', 0))
            
            # Log progress
            if step % 1000 == 0:
                logger.info(f"Step {step} - Balance: ${info.get('balance', 0):.2f} - "
                          f"Total Reward: {total_reward:.2f}")
                          
        # Calculate final metrics
        final_metrics = self.metrics.calculate_metrics()
        
        # Compile results
        backtest_results = {
            'summary': {
                'total_steps': step,
                'total_reward': total_reward,
                'initial_balance': self.initial_balance,
                'final_balance': info.get('balance', 0),
                'final_equity': info.get('balance', 0) + info.get('unrealized_pnl', 0),
                'total_return': (info.get('balance', 0) - self.initial_balance) / self.initial_balance,
                'total_trades': info.get('total_trades', 0),
                'win_rate': info.get('win_rate', 0),
                'max_drawdown': info.get('max_drawdown', 0),
                'sharpe_ratio': info.get('sharpe_ratio', 0),
            },
            'metrics': final_metrics,
            'time_series': self.results,
            'config': {
                'model_path': self.model_path,
                'initial_balance': self.initial_balance,
                'deterministic': deterministic,
                'start_date': start_date,
                'end_date': end_date,
            }
        }
        
        logger.info("Backtesting completed")
        logger.info(f"Final Balance: ${backtest_results['summary']['final_balance']:.2f}")
        logger.info(f"Total Return: {backtest_results['summary']['total_return']:.2%}")
        logger.info(f"Total Trades: {backtest_results['summary']['total_trades']}")
        logger.info(f"Win Rate: {backtest_results['summary']['win_rate']:.2%}")
        
        return backtest_results
        
    def _record_step(self, step: int, info: Dict[str, Any], action: int, reward: float) -> None:
        """Record data for current step."""
        self.results['equity_curve'].append(info.get('balance', 0) + info.get('unrealized_pnl', 0))
        self.results['actions'].append(action)
        self.results['rewards'].append(reward)
        self.results['positions'].append(info.get('position_long', 0) - info.get('position_short', 0))
        self.results['balances'].append(info.get('balance', 0))
        self.results['timestamps'].append(step)
        
    def create_visualizations(
        self,
        results: Dict[str, Any],
        output_dir: str
    ) -> None:
        """
        Create backtesting visualizations.
        
        Args:
            results: Backtesting results
            output_dir: Output directory for plots
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        time_series = results['time_series']
        
        # 1. Equity curve
        plt.figure(figsize=(15, 8))
        plt.plot(time_series['timestamps'], time_series['equity_curve'], linewidth=2)
        plt.axhline(y=self.initial_balance, color='red', linestyle='--', alpha=0.7, 
                   label=f'Initial Balance: ${self.initial_balance:,.0f}')
        plt.xlabel('Time Steps')
        plt.ylabel('Portfolio Value ($)')
        plt.title('Portfolio Equity Curve')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'equity_curve.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Drawdown curve
        equity_curve = np.array(time_series['equity_curve'])
        peak = np.maximum.accumulate(equity_curve)
        drawdown = (peak - equity_curve) / peak
        
        plt.figure(figsize=(15, 6))
        plt.fill_between(time_series['timestamps'], drawdown, alpha=0.7, color='red')
        plt.xlabel('Time Steps')
        plt.ylabel('Drawdown')
        plt.title('Drawdown Curve')
        plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'drawdown_curve.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. Position evolution
        plt.figure(figsize=(15, 6))
        plt.plot(time_series['timestamps'], time_series['positions'], alpha=0.8)
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        plt.xlabel('Time Steps')
        plt.ylabel('Net Position')
        plt.title('Position Evolution')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'position_evolution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 4. Action distribution
        action_names = {0: 'Hold', 1: 'Buy', 2: 'Sell', 3: 'Close'}
        actions = time_series['actions']
        action_counts = {}
        
        for action in actions:
            name = action_names.get(action, f'Action_{action}')
            action_counts[name] = action_counts.get(name, 0) + 1
            
        plt.figure(figsize=(10, 6))
        names = list(action_counts.keys())
        counts = list(action_counts.values())
        plt.bar(names, counts, alpha=0.8)
        plt.xlabel('Action')
        plt.ylabel('Frequency')
        plt.title('Action Distribution')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'action_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 5. Returns distribution
        returns = np.diff(equity_curve) / equity_curve[:-1]
        
        plt.figure(figsize=(10, 6))
        plt.hist(returns, bins=50, alpha=0.7, edgecolor='black')
        plt.axvline(np.mean(returns), color='red', linestyle='--', 
                   label=f'Mean: {np.mean(returns):.4f}')
        plt.xlabel('Daily Returns')
        plt.ylabel('Frequency')
        plt.title('Distribution of Daily Returns')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'returns_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 6. Rolling metrics
        window = min(252, len(equity_curve) // 4)  # Quarterly rolling window
        if window > 10:
            rolling_returns = pd.Series(returns).rolling(window).mean()
            rolling_volatility = pd.Series(returns).rolling(window).std()
            rolling_sharpe = rolling_returns / rolling_volatility
            
            fig, axes = plt.subplots(3, 1, figsize=(15, 12))
            
            # Rolling returns
            axes[0].plot(rolling_returns.index, rolling_returns.values)
            axes[0].set_title(f'Rolling Returns (Window: {window})')
            axes[0].set_ylabel('Returns')
            axes[0].grid(True, alpha=0.3)
            
            # Rolling volatility
            axes[1].plot(rolling_volatility.index, rolling_volatility.values, color='orange')
            axes[1].set_title(f'Rolling Volatility (Window: {window})')
            axes[1].set_ylabel('Volatility')
            axes[1].grid(True, alpha=0.3)
            
            # Rolling Sharpe ratio
            axes[2].plot(rolling_sharpe.index, rolling_sharpe.values, color='green')
            axes[2].set_title(f'Rolling Sharpe Ratio (Window: {window})')
            axes[2].set_xlabel('Time Steps')
            axes[2].set_ylabel('Sharpe Ratio')
            axes[2].grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(output_path / 'rolling_metrics.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        logger.info(f"Visualizations saved to {output_path}")
        
    def save_results(
        self,
        results: Dict[str, Any],
        output_dir: str
    ) -> None:
        """
        Save backtesting results to files.
        
        Args:
            results: Backtesting results
            output_dir: Output directory
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Save summary and metrics
        summary_data = {
            'summary': results['summary'],
            'metrics': results['metrics'],
            'config': results['config']
        }
        
        with open(output_path / 'backtest_summary.json', 'w') as f:
            json.dump(summary_data, f, indent=2, default=str)
            
        # Save time series data
        time_series_df = pd.DataFrame(results['time_series'])
        time_series_df.to_csv(output_path / 'backtest_timeseries.csv', index=False)
        
        logger.info(f"Results saved to {output_path}")
        
    def close(self) -> None:
        """Close backtester and cleanup resources."""
        if hasattr(self, 'env'):
            self.env.close()
        if hasattr(self, 'agent'):
            self.agent.close()


def main(
    model_path: str,
    data_path: Optional[str] = None,
    config_path: str = "configs/environment/default.yaml",
    output_dir: str = "results/backtest",
    initial_balance: float = 10000.0,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    create_plots: bool = True,
) -> None:
    """
    Main backtesting function.
    
    Args:
        model_path: Path to trained model
        data_path: Path to test data (optional)
        config_path: Path to configuration file
        output_dir: Output directory for results
        initial_balance: Initial balance for backtesting
        start_date: Start date for backtesting
        end_date: End date for backtesting
        create_plots: Whether to create visualization plots
    """
    # Setup logging
    setup_logging(level='INFO', log_dir='logs', log_file='backtest.log')
    
    try:
        # Create backtester
        backtester = Backtester(
            model_path=model_path,
            config_path=config_path,
            initial_balance=initial_balance
        )
        
        # Run backtest
        results = backtester.run_backtest(
            start_date=start_date,
            end_date=end_date,
            deterministic=True
        )
        
        # Save results
        backtester.save_results(results, output_dir)
        
        # Create visualizations
        if create_plots:
            backtester.create_visualizations(results, output_dir)
        
        logger.info("Backtesting completed successfully")
        
    except Exception as e:
        logger.error(f"Backtesting failed: {e}")
        raise
    finally:
        if 'backtester' in locals():
            backtester.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Backtest RL-CT model")
    parser.add_argument("model_path", help="Path to trained model")
    parser.add_argument("--data", "-d", help="Path to test data")
    parser.add_argument("--config", "-c", default="configs/environment/default.yaml",
                       help="Path to environment configuration")
    parser.add_argument("--output", "-o", default="results/backtest",
                       help="Output directory for results")
    parser.add_argument("--balance", "-b", type=float, default=10000.0,
                       help="Initial balance for backtesting")
    parser.add_argument("--start-date", help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", help="End date (YYYY-MM-DD)")
    parser.add_argument("--no-plots", action="store_true",
                       help="Skip creating visualization plots")
    
    args = parser.parse_args()
    
    main(
        model_path=args.model_path,
        data_path=args.data,
        config_path=args.config,
        output_dir=args.output,
        initial_balance=args.balance,
        start_date=args.start_date,
        end_date=args.end_date,
        create_plots=not args.no_plots
    )
